"""
简化的模块可见性更新脚本
直接使用SQL语句更新数据库
"""

import os
import sys
from sqlalchemy import create_engine, text
from datetime import datetime

def get_database_url():
    """获取数据库连接URL"""
    # 从环境变量或配置文件获取数据库连接信息
    # 这里使用默认的SQL Server连接
    return "mssql+pyodbc://sa:123456@localhost/StudentsCMSSP?driver=ODBC+Driver+17+for+SQL+Server"

def update_module_visibility():
    """更新模块可见性设置"""
    try:
        # 创建数据库连接
        engine = create_engine(get_database_url())
        
        print("开始更新模块可见性设置...")
        
        # 新增的模块列表及其对应的角色
        module_role_mapping = {
            # 食材管理相关模块
            'ingredient_management': ['仓库管理员', '食堂管理员', '采购员', '学校管理员', '系统管理员', '超级管理员'],
            'ingredient_list': ['仓库管理员', '食堂管理员', '采购员', '学校管理员', '系统管理员', '超级管理员'],
            'ingredient_create': ['学校管理员', '系统管理员', '超级管理员'],
            'ingredient_category': ['仓库管理员', '学校管理员', '系统管理员', '超级管理员'],
            
            # 食谱管理相关模块
            'recipe_category': ['食堂管理员', '学校管理员', '系统管理员', '超级管理员'],
            'recipe_favorite': ['食堂管理员', '学校管理员', '系统管理员', '超级管理员'],
            'recipe_rating': ['食堂管理员', '学校管理员', '系统管理员', '超级管理员'],
            
            # 通知管理相关模块
            'notification': ['仓库管理员', '食堂管理员', '采购员', '学校管理员', '系统管理员', '超级管理员'],
            'notification_center': ['仓库管理员', '食堂管理员', '采购员', '学校管理员', '系统管理员', '超级管理员'],
            'notification_send': ['学校管理员', '系统管理员', '超级管理员'],
            
            # 学校管理相关模块
            'school_admin': ['学校管理员', '系统管理员', '超级管理员'],
            'school_users': ['学校管理员', '系统管理员', '超级管理员'],
            'school_user_add': ['学校管理员', '系统管理员', '超级管理员'],
            
            # 系统管理相关模块
            'security_admin': ['系统管理员', '超级管理员'],
            'inspection_admin': ['系统管理员', '超级管理员']
        }
        
        with engine.connect() as conn:
            # 开始事务
            trans = conn.begin()
            
            try:
                updated_count = 0
                
                for module_id, role_names in module_role_mapping.items():
                    print(f"\n处理模块: {module_id}")
                    
                    for role_name in role_names:
                        # 获取角色ID
                        role_query = text("SELECT id FROM roles WHERE name = :role_name")
                        role_result = conn.execute(role_query, {"role_name": role_name}).fetchone()
                        
                        if not role_result:
                            print(f"  角色 {role_name} 不存在，跳过")
                            continue
                            
                        role_id = role_result[0]
                        
                        # 检查是否已经存在设置
                        check_query = text("""
                            SELECT id, is_visible FROM module_visibility 
                            WHERE module_id = :module_id AND role_id = :role_id
                        """)
                        existing = conn.execute(check_query, {
                            "module_id": module_id,
                            "role_id": role_id
                        }).fetchone()
                        
                        if existing:
                            if not existing[1]:  # is_visible = 0
                                # 更新为可见
                                update_query = text("""
                                    UPDATE module_visibility 
                                    SET is_visible = 1, updated_at = GETDATE()
                                    WHERE id = :id
                                """)
                                conn.execute(update_query, {"id": existing[0]})
                                print(f"  更新角色 {role_name} 的模块 {module_id} 为可见")
                                updated_count += 1
                            else:
                                print(f"  角色 {role_name} 的模块 {module_id} 已经可见，跳过")
                        else:
                            # 创建新的可见性设置
                            insert_query = text("""
                                INSERT INTO module_visibility (module_id, role_id, is_visible, created_at, updated_at)
                                VALUES (:module_id, :role_id, 1, GETDATE(), GETDATE())
                            """)
                            conn.execute(insert_query, {
                                "module_id": module_id,
                                "role_id": role_id
                            })
                            print(f"  添加角色 {role_name} 的模块 {module_id} 为可见")
                            updated_count += 1
                
                # 提交事务
                trans.commit()
                print(f"\n✅ 模块可见性更新完成！共更新了 {updated_count} 个设置")
                return True
                
            except Exception as e:
                # 回滚事务
                trans.rollback()
                print(f"❌ 更新过程中出错: {str(e)}")
                return False
                
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

if __name__ == '__main__':
    success = update_module_visibility()
    if success:
        print("\n🎉 模块可见性更新成功！")
        print("现在可以在后台模块可见性管理页面中看到新增的模块。")
    else:
        print("\n💥 模块可见性更新失败！")
        sys.exit(1)
