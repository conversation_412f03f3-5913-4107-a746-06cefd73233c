2025-06-04 11:30:47,630 INFO: 当前用户: rwhxx2333 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-04 11:30:47,635 INFO: 用户区域ID: 45 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-04 11:30:47,644 INFO: 用户区域名称: 岳阳县荣湾湖小学 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-04 11:30:47,644 INFO: 是否管理员: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-04 11:31:03,509 INFO: 使用库存记录: ID=78, 批次号=B20250604f69ae0, 数量=800.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,510 INFO: 准备更新库存: ID=78, 减少数量=800.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,511 INFO: 使用库存记录: ID=78, 批次号=B20250604f69ae0, 数量=800.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,511 INFO: 准备更新库存: ID=78, 减少数量=700.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,511 INFO: 使用库存记录: ID=80, 批次号=B202506046f53d0, 数量=300.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,512 INFO: 准备更新库存: ID=80, 减少数量=300.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,512 INFO: 使用库存记录: ID=81, 批次号=B20250604fafe22, 数量=300.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,512 INFO: 准备更新库存: ID=81, 减少数量=300.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,513 INFO: 使用库存记录: ID=82, 批次号=B20250604df30a7, 数量=350.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,513 INFO: 准备更新库存: ID=82, 减少数量=350.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,513 INFO: 使用库存记录: ID=83, 批次号=B202506048a97ec, 数量=260.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,513 INFO: 准备更新库存: ID=83, 减少数量=260.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,514 INFO: 使用库存记录: ID=84, 批次号=B2025060440e639, 数量=500.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,514 INFO: 准备更新库存: ID=84, 减少数量=500.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,514 INFO: 使用库存记录: ID=85, 批次号=B2025060456b2e4, 数量=300.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,515 INFO: 准备更新库存: ID=85, 减少数量=300.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,515 INFO: 使用库存记录: ID=86, 批次号=B20250604544798, 数量=800.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,515 INFO: 准备更新库存: ID=86, 减少数量=800.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,516 INFO: 使用库存记录: ID=87, 批次号=B20250604de6bfa, 数量=260.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,516 INFO: 准备更新库存: ID=87, 减少数量=260.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,516 INFO: 使用库存记录: ID=88, 批次号=B20250604478849, 数量=50.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,517 INFO: 准备更新库存: ID=88, 减少数量=50.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,517 INFO: 使用库存记录: ID=89, 批次号=B20250604e801c8, 数量=190.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,517 INFO: 准备更新库存: ID=89, 减少数量=190.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,518 INFO: 使用库存记录: ID=90, 批次号=B20250604d5bf30, 数量=100.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,518 INFO: 准备更新库存: ID=90, 减少数量=100.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,518 INFO: 使用库存记录: ID=86, 批次号=B20250604544798, 数量=800.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,519 INFO: 准备更新库存: ID=86, 减少数量=600.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,519 INFO: 使用库存记录: ID=92, 批次号=B20250604372189, 数量=50.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,519 INFO: 准备更新库存: ID=92, 减少数量=50.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,519 INFO: 使用库存记录: ID=93, 批次号=B202506041a0ca2, 数量=600.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,520 INFO: 准备更新库存: ID=93, 减少数量=600.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,520 INFO: 使用库存记录: ID=94, 批次号=B202506042d08d5, 数量=700.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,520 INFO: 准备更新库存: ID=94, 减少数量=700.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,520 INFO: 使用库存记录: ID=95, 批次号=B20250604f46b1a, 数量=800.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,521 INFO: 准备更新库存: ID=95, 减少数量=800.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,521 INFO: 使用库存记录: ID=96, 批次号=B20250604231d7d, 数量=100.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,521 INFO: 准备更新库存: ID=96, 减少数量=100.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,522 INFO: 使用库存记录: ID=97, 批次号=B2025060452b0b5, 数量=10000.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,525 INFO: 准备更新库存: ID=97, 减少数量=100.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,526 INFO: 使用库存记录: ID=98, 批次号=B20250604cb2e10, 数量=25.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,526 INFO: 准备更新库存: ID=98, 减少数量=25.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,526 INFO: 使用库存记录: ID=98, 批次号=B20250604cb2e10, 数量=25.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,527 INFO: 准备更新库存: ID=98, 减少数量=25.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,527 INFO: 使用库存记录: ID=100, 批次号=B20250604f3b2a3, 数量=590.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,527 INFO: 准备更新库存: ID=100, 减少数量=200.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,528 INFO: 使用库存记录: ID=101, 批次号=B20250604a08044, 数量=400.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,528 INFO: 准备更新库存: ID=101, 减少数量=10.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
2025-06-04 11:31:03,528 INFO: 使用库存记录: ID=101, 批次号=B20250604a08044, 数量=400.00 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:895]
2025-06-04 11:31:03,528 INFO: 准备更新库存: ID=101, 减少数量=5.0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan.py:916]
