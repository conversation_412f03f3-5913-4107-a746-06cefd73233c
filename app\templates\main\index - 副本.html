<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>校园餐智慧食堂平台 - 智能化食堂管理解决方案</title>
  <script nonce="{{ csp_nonce }}" src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script nonce="{{ csp_nonce }}" src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>

  <!-- jQuery (必须在 Bootstrap 之前加载) -->
  <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}?v=1.0.0"></script>

  <!-- 事件处理器管理器（必须在其他脚本之前加载） -->
  <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/event-handler-manager.js') }}"></script>

  <!-- Bootstrap CSS 和 JS (轮播图需要) -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <script nonce="{{ csp_nonce }}" src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- 前端错误监控脚本 -->
  <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/frontend-error-monitor.js') }}"></script>

  <!-- 配置Tailwind CSS -->
  <script nonce="{{ csp_nonce }}">
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#36CBCB',
            accent: '#722ED1',
            dark: '#1D2129',
            light: '#F7F8FA',
            'primary-light': '#E8F3FF',
            'primary-dark': '#0D47A1',
            'neon-blue': '#00BFFF',
            'neon-purple': '#9D4EDD',
            'neon-green': '#00FF9D',
            'dark-blue': '#0B0E2F',
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
            code: ['JetBrains Mono', 'monospace'],
          },
        },
      }
    }
  </script>

  <style type="text/tailwindcss" nonce="{{ csp_nonce }}">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .text-shadow {
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .card-hover {
        @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
      }
      .section-padding {
        @apply py-16 md:py-24;
      }
      .bg-gradient-primary {
        @apply bg-gradient-to-r from-primary to-primary-dark;
      }
      .animate-float {
        animation: float 6s ease-in-out infinite;
      }
      .neon-glow {
        box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
      }
      .neon-text {
        text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
      }
      .data-pulse {
        animation: pulse 2s infinite;
      }
      @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
        100% { transform: translateY(0px); }
      }
      @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.05); opacity: 0.8; }
        100% { transform: scale(1); opacity: 1; }
      }
      .bg-grid {
        background-image: linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
                          linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
        background-size: 20px 20px;
      }
      .clip-path-slant {
        clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
      }
    }

    /* 英雄区域轮播图样式 */
    #heroCarousel {
      border-radius: 1rem 1rem 0 0;
      overflow: hidden;
    }

    #heroCarousel .carousel-item {
      height: 480px;
      transition: transform 0.6s ease-in-out;
    }

    #heroCarousel .carousel-item img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 1rem 1rem 0 0;
    }

    /* 轮播指示器样式 */
    #heroCarousel .carousel-indicators {
      bottom: 20px;
      margin-bottom: 0;
    }

    #heroCarousel .carousel-indicators [data-bs-target] {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin: 0 4px;
      background-color: rgba(255, 255, 255, 0.5);
      border: 2px solid rgba(255, 255, 255, 0.8);
      transition: all 0.3s ease;
      opacity: 0.7;
    }

    #heroCarousel .carousel-indicators .active {
      background-color: #00BFFF;
      border-color: #00BFFF;
      transform: scale(1.2);
      opacity: 1;
    }

    /* 轮播控制按钮样式 */
    #heroCarousel .carousel-control-prev,
    #heroCarousel .carousel-control-next {
      width: 45px;
      height: 45px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(0, 0, 0, 0.4);
      border-radius: 50%;
      transition: all 0.3s ease;
      border: none;
      opacity: 0.8;
    }

    #heroCarousel .carousel-control-prev:hover,
    #heroCarousel .carousel-control-next:hover {
      background: rgba(0, 0, 0, 0.7);
      transform: translateY(-50%) scale(1.1);
      opacity: 1;
    }

    #heroCarousel .carousel-control-prev {
      left: 15px;
    }

    #heroCarousel .carousel-control-next {
      right: 15px;
    }

    #heroCarousel .carousel-control-prev-icon,
    #heroCarousel .carousel-control-next-icon {
      width: 18px;
      height: 18px;
    }

    /* 加载和空状态样式 */
    .hero-carousel-loading,
    .hero-carousel-empty {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 1rem 1rem 0 0;
      min-height: 400px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .hero-carousel-loading .spinner-border {
      width: 3rem;
      height: 3rem;
      border-width: 0.3em;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      #heroCarousel {
        border-radius: 1rem;
      }

      #heroCarousel .carousel-item {
        height: 360px;
      }

      #heroCarousel .carousel-control-prev,
      #heroCarousel .carousel-control-next {
        width: 35px;
        height: 35px;
      }

      #heroCarousel .carousel-control-prev {
        left: 10px;
      }

      #heroCarousel .carousel-control-next {
        right: 10px;
      }

      #heroCarousel .carousel-indicators [data-bs-target] {
        width: 10px;
        height: 10px;
        margin: 0 2px;
      }

      .hero-carousel-loading,
      .hero-carousel-empty {
        min-height: 360px;
      }
    }
  </style>
</head>

<body class="font-inter bg-dark-blue text-white antialiased overflow-x-hidden">
  <!-- 背景网格 -->
  <div class="fixed inset-0 bg-grid z-0 opacity-50"></div>

  <!-- 导航栏 -->
  <header id="navbar" class="fixed w-full top-0 z-50 transition-all duration-300 bg-dark-blue/80 backdrop-blur-md border-b border-primary/20">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16 md:h-20">
        <div class="flex items-center">
          <a href="#" class="flex items-center space-x-2">
            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center neon-glow">
              <i class="fa fa-cutlery text-white text-xl"></i>
            </div>
            <span class="text-xl font-bold text-white">智慧食堂<span class="text-neon-blue neon-text">平台</span></span>
          </a>
        </div>

        <!-- 桌面导航 -->
        <nav class="hidden md:flex space-x-8">
          <a href="#features" class="text-white hover:text-neon-blue transition-colors font-medium">核心功能</a>
          <a href="#advantages" class="text-white hover:text-neon-blue transition-colors font-medium">系统优势</a>
          <a href="#process" class="text-white hover:text-neon-blue transition-colors font-medium">管理流程</a>
          <a href="#contact" class="text-white hover:text-neon-blue transition-colors font-medium">联系我们</a>
        </nav>

        <!-- 移动端菜单按钮 -->
        <div class="md:hidden">
          <button id="menu-toggle" class="text-white hover:text-neon-blue focus:outline-none">
            <i class="fa fa-bars text-2xl"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端导航菜单 -->
    <div id="mobile-menu" class="md:hidden hidden bg-dark-blue/95 backdrop-blur-md border-t border-primary/20">
      <div class="container mx-auto px-4 py-3 space-y-3">
        <a href="#features" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">核心功能</a>
        <a href="#advantages" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">系统优势</a>
        <a href="#process" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">管理流程</a>
        <a href="#contact" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">联系我们</a>
      </div>
    </div>
  </header>

  <!-- 英雄区域 -->
  <section class="pt-24 md:pt-32 pb-16 md:pb-24 bg-gradient-to-b from-primary/5 to-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute -top-40 -left-40 w-80 h-80 bg-neon-blue/20 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-40 -right-40 w-80 h-80 bg-neon-purple/20 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="flex flex-col lg:flex-row items-center">
        <div class="lg:w-1/2 mb-10 lg:mb-0">
          <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight text-white mb-6">
            智慧食堂管理平台<br>
            <span class="text-neon-blue neon-text">全方位智能化解决方案</span>
          </h1>
          <p class="text-lg md:text-xl text-gray-300 mb-8 max-w-xl">
            致力于打造全方位智能化管理体系，实现食品安全可视化、可管控、可追溯，为校园食堂管理提供高效便捷的技术支持。
          </p>
          <div class="flex flex-wrap gap-4">
            <a href="{{ url_for('auth.login') }}" class="bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-8 py-3 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30 flex items-center neon-glow">
              立即登录
              <i class="fa fa-sign-in ml-2"></i>
            </a>
            <a href="{{ url_for('auth.register') }}" class="bg-transparent hover:bg-white/10 text-white border border-neon-green/50 font-medium px-8 py-3 rounded-lg transition-all flex items-center hover:border-neon-green">
              免费注册
              <i class="fa fa-user-plus ml-2"></i>
            </a>
            <a href="#features" class="bg-transparent hover:bg-white/10 text-white border border-neon-blue/50 font-medium px-6 py-3 rounded-lg transition-all flex items-center text-sm">
              了解功能
              <i class="fa fa-arrow-down ml-2"></i>
            </a>
          </div>

          <!-- 数据指标和宣传区域 -->
          <div class="mt-12 flex items-end justify-between">
            <!-- 左侧：数据指标 -->
            <div class="grid grid-cols-3 gap-4 flex-1">
              <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
                <p class="text-neon-blue font-code text-2xl font-bold">99.9%</p>
                <p class="text-sm text-gray-400">系统稳定性</p>
              </div>
              <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
                <p class="text-neon-green font-code text-2xl font-bold">80%</p>
                <p class="text-sm text-gray-400">管理效率提升</p>
              </div>
              <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
                <p class="text-neon-purple font-code text-2xl font-bold">100%</p>
                <p class="text-sm text-gray-400">食品溯源率</p>
              </div>
            </div>


          </div>
        </div>

        <!-- 轮播图区域 -->
        <div class="lg:w-1/2 relative">
          <div class="relative z-10 animate-float">
            <!-- 轮播图容器 -->
            <div class="bg-dark/50 backdrop-blur-md rounded-2xl shadow-2xl border border-primary/30 overflow-hidden">
              <div id="heroCarousel" class="carousel slide">
                <!-- 轮播指示器 -->
                <div class="carousel-indicators" id="heroCarouselIndicators">
                  <!-- 动态生成 -->
                </div>

                <!-- 轮播内容 -->
                <div class="carousel-inner" id="heroCarouselInner">
                  <!-- 动态生成 -->
                </div>

                <!-- 轮播控制按钮 -->
                <button class="carousel-control-prev" type="button" id="heroCarouselPrev">
                  <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                  <span class="visually-hidden">上一张</span>
                </button>
                <button class="carousel-control-next" type="button" id="heroCarouselNext">
                  <span class="carousel-control-next-icon" aria-hidden="true"></span>
                  <span class="visually-hidden">下一张</span>
                </button>

                <!-- 加载状态 -->
                <div class="hero-carousel-loading text-center py-16" id="heroCarouselLoading">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                  <p class="text-white mt-3">正在加载轮播图...</p>
                </div>

                <!-- 空状态 -->
                <div class="hero-carousel-empty text-center py-16" id="heroCarouselEmpty" style="display: none;">
                  <i class="fa fa-image text-gray-400 text-4xl mb-4"></i>
                  <p class="text-gray-400">暂无轮播图内容</p>
                  <p class="text-gray-500 text-sm">请联系管理员添加轮播图</p>
                </div>
              </div>
            </div>
          </div>


        </div>

      </div>
    </div>
  </section>

  <!-- 数据仪表盘 -->
  <section class="py-12 bg-dark/50 relative">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-dark/50 backdrop-blur-md rounded-xl border border-primary/20 p-6 shadow-lg">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <h3 class="text-xl font-bold text-white mb-4 md:mb-0">系统运行数据 <span class="text-neon-blue text-sm font-normal">实时更新</span></h3>
          <div class="flex space-x-3">
            <button class="px-3 py-1 bg-primary/20 text-white text-sm rounded hover:bg-primary/30 transition-colors">今日</button>
            <button class="px-3 py-1 bg-primary/10 text-gray-400 text-sm rounded hover:bg-primary/30 transition-colors">本周</button>
            <button class="px-3 py-1 bg-primary/10 text-gray-400 text-sm rounded hover:bg-primary/30 transition-colors">本月</button>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 图表1 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">食堂运营效率</h4>
            <div class="h-48">
              <canvas id="efficiencyChart"></canvas>
            </div>
          </div>

          <!-- 图表2 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">食品安全检测</h4>
            <div class="h-48">
              <canvas id="safetyChart"></canvas>
            </div>
          </div>

          <!-- 图表3 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">用户满意度</h4>
            <div class="h-48">
              <canvas id="satisfactionChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 核心功能 -->
  <section id="features" class="section-padding bg-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute top-1/4 -left-20 w-60 h-60 bg-neon-purple/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 -right-20 w-60 h-60 bg-neon-blue/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">八大智能化功能，全面保障食堂安全</h2>
        <p class="text-lg text-gray-300">
          我们的智慧食堂平台集成了多项先进功能，从食材采购到餐后服务，全方位提升食堂管理效率与食品安全
        </p>
      </div>

      <!-- 功能分组导航 -->
      <div class="flex justify-center mb-12 space-x-4 overflow-x-auto pb-4">
        <button class="px-6 py-2 rounded-full bg-primary/20 text-white hover:bg-primary/30 transition-colors whitespace-nowrap">
          食品安全管理
        </button>
        <button class="px-6 py-2 rounded-full bg-primary/10 text-gray-400 hover:bg-primary/20 transition-colors whitespace-nowrap">
          运营效率提升
        </button>
        <button class="px-6 py-2 rounded-full bg-primary/10 text-gray-400 hover:bg-primary/20 transition-colors whitespace-nowrap">
          家校互动管理
        </button>
      </div>

      <!-- 功能展示区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 食品安全管理组 -->
        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
            <i class="fa fa-search text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-blue/20 text-neon-blue">食品安全</span>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能检查系统</h3>
            <p class="text-gray-400 mb-4">
            员工通过扫码上传食堂卫生状况、设备运行情况，管理员在线进行评价反馈，实时监控食堂运营状态
          </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-qrcode text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-blue/20 text-neon-blue">食品安全</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">全程溯源</h3>
            <p class="text-gray-400 mb-4">
              实现食品从源头到餐桌的全程可追溯，打造透明化供应链，明确安全责任到人
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-print text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-blue/20 text-neon-blue">食品安全</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">一键式留样标签</h3>
            <p class="text-gray-400 mb-4">
              规范食品留样流程，自动生成留样标签，确保食品安全管理符合规范要求
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <!-- 运营效率提升组 -->
        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-shopping-cart text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-green/20 text-neon-green">运营效率</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">智能采购系统</h3>
            <p class="text-gray-400 mb-4">
              提供供应商灵活选择功能，支持智能价格对比，依据采购需求自动生成采购单，简化采购流程
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-exchange text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-green/20 text-neon-green">运营效率</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">出入库管理</h3>
            <p class="text-gray-400 mb-4">
              对出入库流程进行完整管理，自动生成台账报表，实时监控库存情况，确保库存管理精准高效
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-list text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-green/20 text-neon-green">运营效率</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">灵活菜单管理</h3>
            <p class="text-gray-400 mb-4">
              支持周菜单灵活安排与调整，可直接打印输出，一键导入菜单信息生成带价格及营养分析的带量食谱
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <!-- 家校互动管理组 -->
        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-users text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-purple/20 text-neon-purple">家校互动</span>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">家校共陪餐</h3>
            <p class="text-gray-400 mb-4">
            邀请家长参与陪餐体验，提升食堂管理透明度，加强家校互动沟通，增强家长对食堂的信任度
          </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
            <i class="fa fa-file-text-o text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-purple/20 text-neon-purple">家校互动</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">智能日志生成</h3>
            <p class="text-gray-400 mb-4">
              每日自动生成食堂工作日志，完整记录运营情况，基于数据进行智能分析，为管理决策提供依据
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能演示视频 -->
      <div class="mt-16 bg-dark/30 backdrop-blur-sm rounded-xl p-8 border border-primary/20">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-white mb-2">功能演示</h3>
          <p class="text-gray-400">观看视频了解系统功能详情</p>
        </div>
        <div class="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
          <div class="w-full h-96 bg-dark/50 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <i class="fa fa-play-circle text-6xl text-neon-blue mb-4"></i>
              <p class="text-white">点击播放功能演示视频</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 系统优势 -->
  <section id="advantages" class="section-padding bg-dark/50 relative overflow-hidden">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">为什么选择我们的智慧食堂平台？</h2>
        <p class="text-lg text-gray-300">
          全方位的智能化解决方案，为校园食堂管理带来革命性的改变
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- 优势1 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl p-6 border border-primary/20 group hover:border-neon-blue transition-all duration-300">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-neon-blue/20 rounded-lg flex items-center justify-center group-hover:bg-neon-blue/30 transition-colors">
              <i class="fa fa-shield text-neon-blue text-xl"></i>
            </div>
            <div>
              <h3 class="text-xl font-bold text-white mb-2">全方位安全保障</h3>
              <p class="text-gray-400">
                从食材采购到餐后服务，全程可追溯，确保食品安全无死角
              </p>
            </div>
          </div>
        </div>

        <!-- 优势2 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl p-6 border border-primary/20 group hover:border-neon-green transition-all duration-300">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-neon-green/20 rounded-lg flex items-center justify-center group-hover:bg-neon-green/30 transition-colors">
              <i class="fa fa-line-chart text-neon-green text-xl"></i>
            </div>
            <div>
              <h3 class="text-xl font-bold text-white mb-2">高效运营管理</h3>
              <p class="text-gray-400">
                智能化管理流程，提升运营效率，降低管理成本
              </p>
            </div>
          </div>
        </div>

        <!-- 优势3 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl p-6 border border-primary/20 group hover:border-neon-purple transition-all duration-300">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-neon-purple/20 rounded-lg flex items-center justify-center group-hover:bg-neon-purple/30 transition-colors">
              <i class="fa fa-mobile text-neon-purple text-xl"></i>
            </div>
            <div>
              <h3 class="text-xl font-bold text-white mb-2">便捷移动办公</h3>
              <p class="text-gray-400">
                随时随地处理工作，移动端操作便捷，提高工作效率
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 管理流程 -->
  <section id="process" class="section-padding bg-dark-blue relative overflow-hidden">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">智能化管理流程</h2>
        <p class="text-lg text-gray-300">
          从食材采购到餐后服务，全程智能化管理，提升效率与安全
        </p>
      </div>

      <div class="relative">
        <!-- 流程线 -->
        <div class="absolute left-1/2 top-0 bottom-0 w-0.5 bg-primary/20 transform -translate-x-1/2 hidden md:block"></div>

        <!-- 流程步骤 -->
        <div class="space-y-12">
          <!-- 步骤1 -->
          <div class="relative flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 text-right mb-6 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-2">供应商与食材管理</h3>
              <p class="text-gray-400">
                资质审核、评级，食材分类、规格、质量标准管理
              </p>
            </div>
            <div class="w-12 h-12 bg-neon-blue rounded-full flex items-center justify-center z-10">
              <span class="text-white font-bold">1</span>
            </div>
            <div class="md:w-1/2 md:pl-12">
              <img src="{{ url_for('static', filename='images/1.png') }}" alt="供应商与食材管理" class="rounded-lg shadow-lg">
            </div>
          </div>

          <!-- 步骤2 -->
          <div class="relative flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 text-right mb-6 md:mb-0 order-2 md:order-1">
              <img src="{{ url_for('static', filename='images/9.png') }}" alt="智能采购与入库" class="rounded-lg shadow-lg">
            </div>
            <div class="w-12 h-12 bg-neon-green rounded-full flex items-center justify-center z-10 order-1 md:order-2">
              <span class="text-white font-bold">2</span>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3">
              <h3 class="text-xl font-bold text-white mb-2">智能采购与入库</h3>
              <p class="text-gray-400">
                需求预测、采购执行、严格入库验收与登记
              </p>
            </div>
          </div>

          <!-- 步骤3 -->
          <div class="relative flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 text-right mb-6 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-2">库存监控与控制</h3>
          <p class="text-gray-400">
                实时监控库存、临期预警、盘点管理、损耗控制
              </p>
            </div>
            <div class="w-12 h-12 bg-neon-purple rounded-full flex items-center justify-center z-10">
              <span class="text-white font-bold">3</span>
            </div>
            <div class="md:w-1/2 md:pl-12">
              <img src="{{ url_for('static', filename='images/10.png') }}" alt="库存监控与控制" class="rounded-lg shadow-lg">
            </div>
          </div>

          <!-- 步骤4 -->
          <div class="relative flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 text-right mb-6 md:mb-0 order-2 md:order-1">
              <img src="{{ url_for('static', filename='images/8.png') }}" alt="食谱标准化与营养分析" class="rounded-lg shadow-lg">
            </div>
            <div class="w-12 h-12 bg-neon-blue rounded-full flex items-center justify-center z-10 order-1 md:order-2">
              <span class="text-white font-bold">4</span>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3">
              <h3 class="text-xl font-bold text-white mb-2">食谱标准化与营养分析</h3>
              <p class="text-gray-400">
                制定标准化食谱，计算营养成分与热量，确保均衡
              </p>
            </div>
          </div>

          <!-- 步骤5 -->
          <div class="relative flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 text-right mb-6 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-2">周菜单计划与执行</h3>
              <p class="text-gray-400">
                灵活安排周菜单，考虑口味、季节性、成本，并执行
              </p>
            </div>
            <div class="w-12 h-12 bg-neon-green rounded-full flex items-center justify-center z-10">
              <span class="text-white font-bold">5</span>
            </div>
            <div class="md:w-1/2 md:pl-12">
              <img src="{{ url_for('static', filename='images/5.png') }}" alt="周菜单计划与执行" class="rounded-lg shadow-lg">
            </div>
          </div>

          <!-- 步骤6 -->
          <div class="relative flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 text-right mb-6 md:mb-0 order-2 md:order-1">
              <img src="{{ url_for('static', filename='images/6.png') }}" alt="批次追溯与食品安全" class="rounded-lg shadow-lg">
            </div>
            <div class="w-12 h-12 bg-neon-purple rounded-full flex items-center justify-center z-10 order-1 md:order-2">
              <span class="text-white font-bold">6</span>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3">
              <h3 class="text-xl font-bold text-white mb-2">批次追溯与食品安全</h3>
              <p class="text-gray-400">
                批次管理实现全程追溯，进行食品安全检测和留样
              </p>
            </div>
          </div>

          <!-- 步骤7 -->
          <div class="relative flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 text-right mb-6 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-2">数据采集与运营分析</h3>
              <p class="text-gray-400">
                采集各环节数据，进行运营分析，为决策提供支持
              </p>
            </div>
            <div class="w-12 h-12 bg-neon-blue rounded-full flex items-center justify-center z-10">
              <span class="text-white font-bold">7</span>
            </div>
            <div class="md:w-1/2 md:pl-12">
              <img src="{{ url_for('static', filename='images/11.png') }}" alt="数据采集与运营分析" class="rounded-lg shadow-lg">
            </div>
          </div>

        </div>
      </div>
    </div>
  </section>

  <!-- 联系我们 -->
  <section id="contact" class="section-padding bg-dark/50 relative overflow-hidden">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">联系我们</h2>
        <p class="text-lg text-gray-300">
          如果您有任何问题或需求，请随时与我们联系
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- 联系信息 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl p-8 border border-primary/20">
          <h3 class="text-xl font-bold text-white mb-6">联系方式</h3>

          <!-- 联系方式布局 -->
          <div class="flex flex-col lg:flex-row gap-8">
            <!-- 左侧：联系方式列表 -->
            <div class="flex-1">
              <div class="space-y-4">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                    <i class="fa fa-phone text-neon-blue text-xl"></i>
                  </div>
                  <div>
                    <p class="text-gray-400">电话咨询</p>
                    <p class="text-white font-medium">18373062333</p>
                  </div>
                </div>
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                    <i class="fa fa-envelope text-neon-blue text-xl"></i>
                  </div>
                  <div>
                    <p class="text-gray-400">邮件咨询</p>
                    <p class="text-white font-medium"><EMAIL></p>
                  </div>
                </div>
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                    <i class="fa fa-map-marker text-neon-blue text-xl"></i>
                  </div>
                  <div>
                    <p class="text-gray-400">联系地址</p>
                    <p class="text-white font-medium">湖南.岳阳</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧：微信二维码 -->
            <div class="flex-shrink-0">
              <div class="text-center">
                <h4 class="text-lg font-bold text-white mb-4">
                  <i class="fa fa-weixin text-neon-green mr-2"></i>
                  微信咨询
                </h4>
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-neon-green/30">
                  <img src="{{ url_for('static', filename='images/WX.JPG') }}"
                       alt="微信二维码"
                       class="w-32 h-32 rounded-lg shadow-lg mx-auto object-cover">
                  <p class="text-neon-green text-sm mt-4 font-medium">扫码添加微信</p>
                  <p class="text-gray-400 text-xs">快速获得技术支持</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 免费使用宣传语 -->
          <div class="mt-6 bg-gradient-to-r from-neon-green/20 to-neon-blue/20 rounded-lg p-4 border border-neon-green/30">
            <div class="text-center">
              <p class="text-neon-green font-bold text-lg">
                <i class="fa fa-gift mr-2"></i>
                免费使用！永不停机
              </p>
              <p class="text-gray-300 text-sm mt-1">
                专业技术支持 · 7x24小时服务
              </p>
            </div>
          </div>
        </div>

        <!-- 联系表单 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl p-8 border border-primary/20">
          <h3 class="text-xl font-bold text-white mb-6">在线咨询</h3>
          <form id="consultationForm" class="space-y-4">
            <div>
              <label class="block text-gray-400 mb-2">姓名</label>
              <input type="text" id="name" name="name" required
                     class="w-full bg-dark/50 border border-primary/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-blue"
                     placeholder="请输入您的姓名">
            </div>
            <div>
              <label class="block text-gray-400 mb-2">联系方式类型</label>
              <select id="contact_type" name="contact_type"
                      class="w-full bg-dark/50 border border-primary/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-blue">
                <option value="微信">微信</option>
                <option value="电话">电话</option>
                <option value="邮箱">邮箱</option>
              </select>
            </div>
            <div>
              <label class="block text-gray-400 mb-2">联系方式</label>
              <input type="text" id="contact_value" name="contact_value" required
                     class="w-full bg-dark/50 border border-primary/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-blue"
                     placeholder="请输入您的微信号">
            </div>
            <div>
              <label class="block text-gray-400 mb-2">咨询内容</label>
              <textarea id="content" name="content" required
                        class="w-full bg-dark/50 border border-primary/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-blue h-32"
                        placeholder="请详细描述您的咨询内容，我们会尽快回复您"></textarea>
            </div>
            <button type="submit" id="submitBtn"
                    class="w-full bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-8 py-3 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30">
              提交咨询
            </button>
          </form>

          <!-- 提示消息区域 -->
          <div id="messageArea" class="mt-4 hidden">
            <div id="messageContent" class="p-4 rounded-lg text-center font-medium"></div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 页脚 -->
  <footer class="bg-dark-blue border-t border-primary/20 py-12">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- 公司信息 -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <i class="fa fa-cutlery text-white text-xl"></i>
            </div>
            <span class="text-xl font-bold text-white">智慧食堂<span class="text-neon-blue">平台</span></span>
          </div>
          <p class="text-gray-400 mb-4">
            致力于打造全方位智能化管理体系，实现食品安全可视化、可管控、可追溯，为校园食堂管理提供高效便捷的技术支持。
          </p>

          <!-- 免费使用宣传 -->
          <div class="bg-gradient-to-r from-neon-green/10 to-neon-blue/10 rounded-lg p-3 mb-4 border border-neon-green/20">
            <p class="text-neon-green font-bold text-lg">
              <i class="fa fa-gift mr-2"></i>
              免费使用！永不停机
            </p>
            <p class="text-gray-300 text-sm">
              专业技术支持 · 7x24小时服务 · 无限制使用
            </p>
          </div>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">
              <i class="fa fa-weixin text-xl"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">
              <i class="fa fa-weibo text-xl"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">
              <i class="fa fa-qq text-xl"></i>
            </a>
          </div>
        </div>

        <!-- 快速链接 -->
        <div>
          <h4 class="text-white font-bold mb-4">快速链接</h4>
          <ul class="space-y-2">
            <li><a href="#features" class="text-gray-400 hover:text-neon-blue transition-colors">核心功能</a></li>
            <li><a href="#advantages" class="text-gray-400 hover:text-neon-blue transition-colors">系统优势</a></li>
            <li><a href="#process" class="text-gray-400 hover:text-neon-blue transition-colors">管理流程</a></li>
            <li><a href="#contact" class="text-gray-400 hover:text-neon-blue transition-colors">联系我们</a></li>
          </ul>
        </div>

        <!-- 联系方式 -->
        <div>
          <h4 class="text-white font-bold mb-4">联系方式</h4>
          <ul class="space-y-2">
            <li class="text-gray-400">
              <i class="fa fa-phone mr-2"></i>
              18373062333
            </li>
            <li class="text-gray-400">
              <i class="fa fa-envelope mr-2"></i>
              <EMAIL>
            </li>
            <li class="text-gray-400">
              <i class="fa fa-map-marker mr-2"></i>
              湖南.岳阳
            </li>
          </ul>
        </div>
      </div>

      <div class="border-t border-primary/20 mt-8 pt-8 text-center">
          <p class="text-gray-400">
          © 2025 智慧食堂平台. All rights reserved.
        </p>
      </div>
    </div>
  </footer>

  <!-- 图表初始化脚本 -->
  <script nonce="{{ csp_nonce }}">
    // 食堂运营效率图表
    const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');
    new Chart(efficiencyCtx, {
      type: 'line',
      data: {
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        datasets: [{
          label: '运营效率',
          data: [85, 88, 90, 87, 92, 89, 91],
          borderColor: '#00BFFF',
          backgroundColor: 'rgba(0, 191, 255, 0.1)',
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            ticks: {
              color: '#9CA3AF'
            }
          },
          x: {
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            ticks: {
              color: '#9CA3AF'
            }
          }
        }
      }
    });

    // 食品安全检测图表
    const safetyCtx = document.getElementById('safetyChart').getContext('2d');
    new Chart(safetyCtx, {
      type: 'doughnut',
      data: {
        labels: ['合格', '待改进', '不合格'],
        datasets: [{
          data: [95, 4, 1],
          backgroundColor: [
            '#00FF9D',
            '#FFD700',
            '#FF4444'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              color: '#9CA3AF'
            }
          }
        }
      }
    });

    // 用户满意度图表
    const satisfactionCtx = document.getElementById('satisfactionChart').getContext('2d');
    new Chart(satisfactionCtx, {
      type: 'bar',
      data: {
        labels: ['菜品质量', '服务态度', '环境卫生', '价格合理', '营养均衡'],
        datasets: [{
          label: '满意度',
          data: [92, 88, 90, 85, 87],
          backgroundColor: '#9D4EDD'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            ticks: {
              color: '#9CA3AF'
            }
          },
          x: {
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            ticks: {
              color: '#9CA3AF'
            }
          }
        }
      }
    });

    // 移动端菜单切换
    document.getElementById('menu-toggle').addEventListener('click', function() {
      document.getElementById('mobile-menu').classList.toggle('hidden');
    });

    // 滚动动画
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-fade-in');
        }
      });
    });

    document.querySelectorAll('.card-hover').forEach((el) => observer.observe(el));

    // 简单有效的轮播图实现
    function initHeroCarousel() {
      const carouselContainer = document.getElementById('heroCarousel');
      const indicatorsContainer = document.getElementById('heroCarouselIndicators');
      const innerContainer = document.getElementById('heroCarouselInner');
      const loadingElement = document.getElementById('heroCarouselLoading');
      const emptyElement = document.getElementById('heroCarouselEmpty');

      // 加载轮播图数据
      fetch('/api/carousel/list')
        .then(response => response.json())
        .then(data => {
          console.log('轮播图API返回数据:', data); // 调试日志

          // 检查数据格式：可能是直接数组，也可能是包含success字段的对象
          let carousels = [];
          if (Array.isArray(data)) {
            carousels = data;
          } else if (data.success && data.data) {
            carousels = data.data;
          } else if (data.data) {
            carousels = data.data;
          }

          if (carousels && carousels.length > 0) {
            // 有数据，创建轮播图
            createCarouselSlides(carousels, indicatorsContainer, innerContainer);
            loadingElement.style.display = 'none';

            // 初始化Bootstrap轮播
            const carousel = new bootstrap.Carousel(carouselContainer, {
              interval: 4000,
              wrap: true,
              touch: true,
              pause: 'hover'
            });

            // 手动绑定控制按钮事件
            const prevBtn = document.getElementById('heroCarouselPrev');
            const nextBtn = document.getElementById('heroCarouselNext');

            if (prevBtn) {
              prevBtn.addEventListener('click', () => carousel.prev());
            }
            if (nextBtn) {
              nextBtn.addEventListener('click', () => carousel.next());
            }

            // 绑定指示器点击事件
            const indicators = indicatorsContainer.querySelectorAll('button');
            indicators.forEach((indicator, index) => {
              indicator.addEventListener('click', () => {
                carousel.to(index);
              });
            });
          } else {
            // 无数据，显示空状态
            console.log('没有轮播图数据');
            showEmptyState(loadingElement, emptyElement, carouselContainer);
          }
        })
        .catch(error => {
          console.error('加载轮播图失败:', error);
          showEmptyState(loadingElement, emptyElement, carouselContainer);
        });
    }

    function createCarouselSlides(items, indicatorsContainer, innerContainer) {
      // 清空容器
      indicatorsContainer.innerHTML = '';
      innerContainer.innerHTML = '';

      items.forEach((item, index) => {
        // 创建指示器
        const indicator = document.createElement('button');
        indicator.type = 'button';
        indicator.setAttribute('aria-label', `轮播图 ${index + 1}`);
        indicator.dataset.slideIndex = index.toString();
        if (index === 0) {
          indicator.classList.add('active');
          indicator.setAttribute('aria-current', 'true');
        }
        indicatorsContainer.appendChild(indicator);

        // 创建轮播项
        const slide = document.createElement('div');
        slide.className = `carousel-item${index === 0 ? ' active' : ''}`;

        const img = document.createElement('img');
        img.src = item.image_path;
        img.alt = item.title || '系统展示';
        img.className = 'd-block w-100';
        img.style.height = '480px';
        img.style.objectFit = 'cover';

        slide.appendChild(img);

        // 添加点击跳转
        if (item.link_url && item.link_url !== '#') {
          slide.style.cursor = 'pointer';
          slide.addEventListener('click', () => {
            if (item.link_url.startsWith('http')) {
              window.open(item.link_url, '_blank');
            } else {
              window.location.href = item.link_url;
            }
          });
        }

        innerContainer.appendChild(slide);
      });
    }

    function showEmptyState(loadingElement, emptyElement, carouselContainer) {
      loadingElement.style.display = 'none';
      emptyElement.style.display = 'block';

      // 隐藏轮播控制元素
      const indicators = carouselContainer.querySelector('.carousel-indicators');
      const prevBtn = carouselContainer.querySelector('.carousel-control-prev');
      const nextBtn = carouselContainer.querySelector('.carousel-control-next');

      if (indicators) indicators.style.display = 'none';
      if (prevBtn) prevBtn.style.display = 'none';
      if (nextBtn) nextBtn.style.display = 'none';
    }

    // 初始化轮播图
    initHeroCarousel();

    // 在线咨询表单处理
    const consultationForm = document.getElementById('consultationForm');
    const submitBtn = document.getElementById('submitBtn');
    const messageArea = document.getElementById('messageArea');
    const messageContent = document.getElementById('messageContent');
    const contactTypeSelect = document.getElementById('contact_type');
    const contactValueInput = document.getElementById('contact_value');

    // 联系方式类型变化时更新占位符
    contactTypeSelect.addEventListener('change', function() {
      const contactType = this.value;
      if (contactType === '微信') {
        contactValueInput.placeholder = '请输入您的微信号';
      } else if (contactType === '电话') {
        contactValueInput.placeholder = '请输入您的手机号码';
      } else if (contactType === '邮箱') {
        contactValueInput.placeholder = '请输入您的邮箱地址';
      }
    });

    // 表单提交处理
    consultationForm.addEventListener('submit', async function(e) {
      e.preventDefault();

      // 禁用提交按钮，防止重复提交
      submitBtn.disabled = true;
      submitBtn.textContent = '提交中...';

      // 获取表单数据
      const formData = {
        name: document.getElementById('name').value.trim(),
        contact_type: document.getElementById('contact_type').value,
        contact_value: document.getElementById('contact_value').value.trim(),
        content: document.getElementById('content').value.trim()
      };

      try {
        // 发送请求
        const response = await fetch('/consultation/api/submit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData)
        });

        const result = await response.json();

        // 显示结果消息
        showMessage(result.message, result.success ? 'success' : 'error');

        // 如果提交成功，清空表单
        if (result.success) {
          consultationForm.reset();
          contactValueInput.placeholder = '请输入您的微信号';
        }

      } catch (error) {
        console.error('提交失败:', error);
        showMessage('网络错误，请稍后重试', 'error');
      } finally {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.textContent = '提交咨询';
      }
    });

    // 显示消息函数
    function showMessage(message, type) {
      messageContent.textContent = message;
      messageContent.className = 'p-4 rounded-lg text-center font-medium';

      if (type === 'success') {
        messageContent.classList.add('bg-green-500/20', 'border', 'border-green-500/30', 'text-green-400');
      } else {
        messageContent.classList.add('bg-red-500/20', 'border', 'border-red-500/30', 'text-red-400');
      }

      messageArea.classList.remove('hidden');

      // 3秒后自动隐藏消息
      setTimeout(() => {
        messageArea.classList.add('hidden');
      }, 3000);
    }
  </script>
</body>
</html>