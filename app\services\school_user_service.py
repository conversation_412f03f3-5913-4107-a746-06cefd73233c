from flask_login import current_user
from app import db
from app.models import User, Role, UserRole, AdministrativeArea
from app.models_visibility import ModuleVisibility
from app.services.base_service import BaseService
import json

class SchoolUserService(BaseService):
    """学校用户管理服务"""

    @staticmethod
    def get_school_users(page=1, per_page=20):
        """获取当前学校的所有用户"""
        school_area = current_user.get_current_area()
        if not school_area:
            return None

        # 获取学校下的所有子区域ID
        sub_area_ids = [area.id for area in school_area.children]
        all_area_ids = [school_area.id] + sub_area_ids

        # 查询属于这些区域的用户
        return User.query.filter(User.area_id.in_(all_area_ids)).order_by(User.id.desc()).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

    @staticmethod
    def create_school_user(data):
        """创建学校用户"""
        school_area = current_user.get_current_area()
        if not school_area:
            raise ValueError("当前用户没有关联学校")

        # 直接使用学校区域，不再支持子区域选择
        area_id = school_area.id
        area_level = 3  # 学校级别

        # 创建用户
        user = User(
            username=data['username'],
            email=data.get('email', ''),
            real_name=data.get('real_name', ''),
            phone=data.get('phone', ''),
            status=data.get('status', 1),
            area_id=area_id,
            area_level=area_level
        )

        # 设置密码
        if data.get('password'):
            user.set_password(data['password'])
        else:
            user.set_password('123456')  # 默认密码

        db.session.add(user)
        db.session.flush()  # 获取用户ID

        # 添加角色并设置默认权限和模块可见性
        for role_id in data.get('roles', []):
            # 检查角色是否是系统管理员或超级管理员
            role = Role.query.get(role_id)
            if role and role.name in ['系统管理员', '超级管理员']:
                continue  # 跳过这些角色

            user_role = UserRole(user_id=user.id, role_id=role_id)
            db.session.add(user_role)

            # 设置角色的默认模块可见性
            SchoolUserService.set_default_module_visibility(role)

        db.session.commit()
        return user

    @staticmethod
    def set_default_module_visibility(role):
        """设置角色的默认模块可见性"""
        # 角色默认可见模块配置
        role_default_modules = {
            '仓库管理员': [
                'inventory', 'stock_in', 'stock_out', 'supplier', 'material_batch',
                'ingredient_management', 'ingredient_list', 'ingredient_category',
                'traceability', 'notification', 'notification_center'
            ],
            '食堂管理员': [
                'daily_management', 'weekly_menu', 'recipe_management', 'recipe_category',
                'recipe_favorite', 'recipe_rating', 'ingredient_management', 'ingredient_list',
                'traceability', 'food_trace', 'food_sample', 'notification', 'notification_center'
            ],
            '采购员': [
                'purchase', 'purchase_order_list', 'purchase_order_create', 'supplier_list',
                'supplier_product_list', 'ingredient_management', 'ingredient_list',
                'notification', 'notification_center'
            ],
            '学校管理员': [
                'home', 'daily_management', 'weekly_menu', 'recipe_management', 'purchase',
                'inventory', 'ingredient_management', 'traceability', 'supplier', 'employee',
                'notification', 'school_admin', 'school_users', 'area'
            ]
        }

        # 如果角色名称在预定义配置中
        if role.name in role_default_modules:
            # 获取该角色默认可见的模块列表
            modules = role_default_modules[role.name]

            # 为每个模块设置可见性
            for module_id in modules:
                # 检查是否已存在可见性设置
                existing = ModuleVisibility.query.filter_by(
                    module_id=module_id,
                    role_id=role.id
                ).first()

                if existing:
                    # 更新现有设置
                    existing.is_visible = 1
                else:
                    # 创建新的可见性设置
                    ModuleVisibility.set_visibility(
                        module_id=module_id,
                        role_id=role.id,
                        is_visible=1
                    )