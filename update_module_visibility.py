#!/usr/bin/env python3
"""
更新模块可见性设置脚本
将新增的模块添加到现有角色的可见性配置中
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import Role
from app.models_visibility import ModuleVisibility
from app.services.school_user_service import SchoolUserService

def update_module_visibility():
    """更新模块可见性设置"""
    app = create_app()
    
    with app.app_context():
        try:
            print("开始更新模块可见性设置...")
            
            # 获取所有角色
            roles = Role.query.all()
            print(f"找到 {len(roles)} 个角色")
            
            # 新增的模块列表
            new_modules = [
                'ingredient_management',
                'ingredient_list', 
                'ingredient_create',
                'ingredient_category',
                'recipe_category',
                'recipe_favorite', 
                'recipe_rating',
                'notification',
                'notification_center',
                'notification_send',
                'school_admin',
                'school_users',
                'school_user_add',
                'security_admin',
                'inspection_admin'
            ]
            
            # 角色默认可见模块配置
            role_default_modules = {
                '仓库管理员': [
                    'ingredient_management', 'ingredient_list', 'ingredient_category',
                    'notification', 'notification_center'
                ],
                '食堂管理员': [
                    'recipe_category', 'recipe_favorite', 'recipe_rating', 
                    'ingredient_management', 'ingredient_list',
                    'notification', 'notification_center'
                ],
                '采购员': [
                    'ingredient_management', 'ingredient_list',
                    'notification', 'notification_center'
                ],
                '学校管理员': [
                    'ingredient_management', 'ingredient_list', 'ingredient_create',
                    'ingredient_category', 'recipe_category', 'recipe_favorite', 
                    'recipe_rating', 'notification', 'notification_center',
                    'notification_send', 'school_admin', 'school_users', 'school_user_add'
                ],
                '系统管理员': [
                    'ingredient_management', 'ingredient_list', 'ingredient_create',
                    'ingredient_category', 'recipe_category', 'recipe_favorite', 
                    'recipe_rating', 'notification', 'notification_center',
                    'notification_send', 'school_admin', 'school_users', 'school_user_add',
                    'security_admin', 'inspection_admin'
                ],
                '超级管理员': new_modules  # 超级管理员可以看到所有新模块
            }
            
            updated_count = 0
            
            for role in roles:
                print(f"\n处理角色: {role.name}")
                
                # 获取该角色应该可见的新模块
                visible_modules = role_default_modules.get(role.name, [])
                
                if not visible_modules:
                    print(f"  角色 {role.name} 没有预定义的新模块配置，跳过")
                    continue
                
                # 为每个新模块设置可见性
                for module_id in visible_modules:
                    try:
                        # 检查是否已经存在设置
                        existing = ModuleVisibility.query.filter_by(
                            module_id=module_id,
                            role_id=role.id
                        ).first()
                        
                        if existing:
                            if not existing.is_visible:
                                existing.is_visible = 1
                                print(f"  更新模块 {module_id} 为可见")
                                updated_count += 1
                            else:
                                print(f"  模块 {module_id} 已经可见，跳过")
                        else:
                            # 创建新的可见性设置
                            ModuleVisibility.set_visibility(
                                module_id=module_id,
                                role_id=role.id,
                                is_visible=True
                            )
                            print(f"  添加模块 {module_id} 为可见")
                            updated_count += 1
                            
                    except Exception as e:
                        print(f"  设置模块 {module_id} 可见性失败: {str(e)}")
                        continue
            
            # 提交所有更改
            db.session.commit()
            print(f"\n✅ 模块可见性更新完成！共更新了 {updated_count} 个设置")
            
        except Exception as e:
            print(f"❌ 更新失败: {str(e)}")
            db.session.rollback()
            return False
            
    return True

if __name__ == '__main__':
    success = update_module_visibility()
    if success:
        print("\n🎉 模块可见性更新成功！")
        print("现在可以在后台模块可见性管理页面中看到新增的模块。")
    else:
        print("\n💥 模块可见性更新失败！")
        sys.exit(1)
