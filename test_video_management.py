#!/usr/bin/env python3
"""
测试视频管理功能
验证视频上传、编辑、删除功能是否正常工作
"""

import sys
import os
import requests
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_video_management_endpoints():
    """测试视频管理API端点"""
    base_url = "http://127.0.0.1:8080"
    
    # 测试端点列表
    endpoints = [
        {
            'url': f'{base_url}/admin/guide/videos',
            'method': 'GET',
            'description': '视频管理页面'
        },
        {
            'url': f'{base_url}/admin/guide/videos/upload',
            'method': 'POST',
            'description': '视频上传API'
        },
        {
            'url': f'{base_url}/admin/guide/videos/delete',
            'method': 'POST',
            'description': '视频删除API'
        },
        {
            'url': f'{base_url}/admin/guide/videos/edit',
            'method': 'POST',
            'description': '视频编辑API'
        }
    ]
    
    print("🧪 开始测试视频管理功能...")
    print("=" * 50)
    
    for endpoint in endpoints:
        print(f"\n📍 测试: {endpoint['description']}")
        print(f"   URL: {endpoint['url']}")
        print(f"   方法: {endpoint['method']}")
        
        try:
            if endpoint['method'] == 'GET':
                response = requests.get(endpoint['url'], timeout=5)
            else:
                # 对于POST请求，发送空数据来测试端点是否存在
                response = requests.post(endpoint['url'], data={}, timeout=5)
            
            if response.status_code == 200:
                print(f"   ✅ 状态: 正常 (200)")
            elif response.status_code == 302:
                print(f"   🔄 状态: 重定向 (302) - 可能需要登录")
            elif response.status_code == 401:
                print(f"   🔐 状态: 需要认证 (401) - 需要登录")
            elif response.status_code == 403:
                print(f"   🚫 状态: 权限不足 (403) - 需要管理员权限")
            elif response.status_code == 404:
                print(f"   ❌ 状态: 端点不存在 (404)")
            elif response.status_code == 405:
                print(f"   ⚠️  状态: 方法不允许 (405)")
            else:
                print(f"   ⚠️  状态: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   💥 错误: 无法连接到服务器")
        except requests.exceptions.Timeout:
            print(f"   ⏰ 错误: 请求超时")
        except Exception as e:
            print(f"   💥 错误: {str(e)}")
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print("1. 如果看到 200 状态码，说明端点正常工作")
    print("2. 如果看到 302/401/403，说明端点存在但需要登录或权限")
    print("3. 如果看到 404，说明端点不存在，需要检查路由配置")
    print("4. 如果看到连接错误，请确保服务器正在运行")

def check_video_directories():
    """检查视频目录结构"""
    print("\n📁 检查视频目录结构...")
    print("=" * 50)
    
    static_dir = os.path.join(os.path.dirname(__file__), 'app', 'static')
    videos_dir = os.path.join(static_dir, 'videos')
    
    print(f"静态文件目录: {static_dir}")
    print(f"视频目录: {videos_dir}")
    
    if os.path.exists(static_dir):
        print("✅ 静态文件目录存在")
    else:
        print("❌ 静态文件目录不存在")
        return
    
    if os.path.exists(videos_dir):
        print("✅ 视频目录存在")
        
        # 列出视频子目录
        try:
            subdirs = [d for d in os.listdir(videos_dir) if os.path.isdir(os.path.join(videos_dir, d))]
            if subdirs:
                print(f"📂 视频子目录: {', '.join(subdirs)}")
                
                # 检查每个子目录中的文件
                for subdir in subdirs:
                    subdir_path = os.path.join(videos_dir, subdir)
                    files = [f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))]
                    if files:
                        print(f"   📄 {subdir}: {len(files)} 个文件")
                    else:
                        print(f"   📄 {subdir}: 空目录")
            else:
                print("📂 视频目录为空")
        except Exception as e:
            print(f"❌ 读取视频目录失败: {str(e)}")
    else:
        print("⚠️  视频目录不存在，将在首次上传时创建")

def check_video_service():
    """检查视频服务配置"""
    print("\n🔧 检查视频服务配置...")
    print("=" * 50)
    
    try:
        from app.services.video_guide_service import VideoGuideService
        
        # 获取所有视频资源
        video_resources = VideoGuideService.get_all_videos()
        
        print(f"✅ 视频服务正常加载")
        print(f"📊 配置的视频模块数量: {len(video_resources)}")
        
        total_videos = 0
        for step_name, step_data in video_resources.items():
            videos = step_data.get('videos', [])
            total_videos += len(videos)
            print(f"   📹 {step_name}: {len(videos)} 个视频")
        
        print(f"📊 总视频数量: {total_videos}")
        
    except ImportError as e:
        print(f"❌ 无法导入视频服务: {str(e)}")
    except Exception as e:
        print(f"❌ 视频服务检查失败: {str(e)}")

def main():
    """主函数"""
    print("🎬 视频管理功能测试工具")
    print("=" * 50)
    
    # 检查视频服务配置
    check_video_service()
    
    # 检查视频目录结构
    check_video_directories()
    
    # 测试API端点
    test_video_management_endpoints()
    
    print("\n🎯 测试完成！")
    print("\n💡 使用建议:")
    print("1. 确保服务器正在运行: python run.py")
    print("2. 使用管理员账户登录后台")
    print("3. 访问: http://127.0.0.1:8080/admin/guide/videos")
    print("4. 测试上传、编辑、删除功能")

if __name__ == '__main__':
    main()
