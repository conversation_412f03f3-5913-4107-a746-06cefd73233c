# 模块可见性管理更新说明

## 问题描述
项目中有几个模块还没有纳入到后台模块可见性管理中，导致这些模块无法通过后台进行统一的权限控制。

## 已完成的工作

### 1. 分析未纳入管理的模块
通过全面分析项目代码，发现以下模块还没有纳入到 `MENU_CONFIG` 中：

#### 新增的主要模块：
- **食材管理模块** (`ingredient_management`)
  - 食材列表 (`ingredient_list`)
  - 添加食材 (`ingredient_create`) 
  - 食材分类 (`ingredient_category`)

- **食谱扩展模块**
  - 食谱分类 (`recipe_category`)
  - 食谱收藏 (`recipe_favorite`)
  - 食谱评分 (`recipe_rating`)

- **通知管理模块** (`notification`)
  - 通知中心 (`notification_center`)
  - 发送通知 (`notification_send`)

- **学校管理模块** (`school_admin`)
  - 学校用户管理 (`school_users`)
  - 添加学校用户 (`school_user_add`)

- **系统管理扩展模块**
  - 安全管理 (`security_admin`)
  - 入库检查管理 (`inspection_admin`)

### 2. 更新菜单配置
已更新 `app/utils/menu.py` 文件中的 `MENU_CONFIG`，将所有未纳入的模块添加到相应的菜单分类中：

#### 新增的菜单结构：
```python
# 新增独立的食材管理主菜单
{
    'id': 'ingredient_management',
    'name': '食材管理',
    'icon': 'fas fa-carrot',
    'module': 'ingredient',
    'action': 'view',
    'children': [...]
}

# 扩展周菜单管理的子菜单
{
    'id': 'recipe_category',
    'name': '食谱分类',
    'url': 'recipe_category.index',
    'module': 'recipe',
    'action': 'view'
}

# 新增通知管理主菜单
{
    'id': 'notification',
    'name': '通知管理',
    'icon': 'fas fa-bell',
    'module': 'notification',
    'action': 'view',
    'children': [...]
}

# 新增学校管理主菜单
{
    'id': 'school_admin',
    'name': '学校管理',
    'icon': 'fas fa-school',
    'module': 'user',
    'action': 'view',
    'area_level': 3,  # 仅学校级别用户可见
    'children': [...]
}
```

### 3. 更新角色默认可见性配置
已更新 `app/services/school_user_service.py` 中的角色默认模块可见性配置：

#### 各角色的新增可见模块：
- **仓库管理员**: 食材管理、通知管理
- **食堂管理员**: 食材管理、食谱扩展、通知管理
- **采购员**: 食材管理、通知管理
- **学校管理员**: 食材管理、食谱扩展、通知管理、学校管理
- **系统管理员**: 所有新增模块包括安全管理
- **超级管理员**: 所有新增模块

### 4. 创建数据库更新脚本
创建了两个脚本用于更新现有角色的模块可见性设置：

1. **update_module_visibility.py** - 完整的Flask应用脚本
2. **simple_update_visibility.py** - 简化的SQL直接更新脚本

## 验证方法

### 1. 访问模块可见性管理页面
打开浏览器访问：`http://127.0.0.1:8080/admin/module-visibility`

### 2. 检查新增模块
在模块可见性管理页面中，应该能看到以下新增的模块：

#### 主模块：
- 食材管理 (ingredient_management)
- 通知管理 (notification)  
- 学校管理 (school_admin)

#### 子模块：
- 食材列表 (ingredient_list)
- 添加食材 (ingredient_create)
- 食材分类 (ingredient_category)
- 食谱分类 (recipe_category)
- 食谱收藏 (recipe_favorite)
- 食谱评分 (recipe_rating)
- 通知中心 (notification_center)
- 发送通知 (notification_send)
- 学校用户管理 (school_users)
- 添加学校用户 (school_user_add)
- 安全管理 (security_admin)
- 入库检查管理 (inspection_admin)

### 3. 测试权限控制
1. 选择不同的角色
2. 切换各个新增模块的可见性开关
3. 验证设置是否正确保存
4. 使用对应角色的用户登录，检查菜单是否按预期显示

## 运行更新脚本（可选）

如果需要为现有角色自动设置新模块的默认可见性，可以运行：

```bash
# 方法1：使用Flask应用脚本
python update_module_visibility.py

# 方法2：使用简化SQL脚本  
python simple_update_visibility.py
```

## 注意事项

1. **权限控制**: 新增的模块都配置了相应的权限要求，确保只有有权限的用户才能访问
2. **区域级别限制**: 学校管理模块设置了 `area_level: 3`，只有学校级别的用户才能看到
3. **管理员专用**: 安全管理和入库检查管理模块设置了 `admin_only: True`，只有管理员才能访问
4. **向后兼容**: 所有现有的模块配置保持不变，只是新增了缺失的模块

## 预期效果

完成更新后，管理员可以在后台模块可见性管理页面中：
- 看到所有项目中的功能模块
- 为不同角色精确控制模块可见性
- 实现更细粒度的权限管理
- 提供更好的用户体验和安全性

这样就实现了项目中所有模块的统一管理，不再有遗漏的功能模块。
