2025-06-04 11:27:02,864 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:430]
2025-06-04 11:27:02,869 INFO: 星期几: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:436]
2025-06-04 11:27:02,874 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:445]
2025-06-04 11:27:02,877 INFO: 周菜单 39 中找到 4 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:456]
2025-06-04 11:27:02,880 INFO: 分析食谱: 🏫 米饭, recipe_id=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,883 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,890 INFO: 分析食谱: 🏫 香干炒肉, recipe_id=410 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,891 INFO: 找到 2 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,895 INFO: 分析食谱: 🏫 清炒生菜, recipe_id=400 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,896 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,898 INFO: 分析食谱: 🏫 花菜炒肉, recipe_id=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,899 INFO: 找到 3 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,904 INFO: 查询餐次: 午餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-04 11:27:02,906 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:370]
2025-06-04 11:27:02,907 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-04 11:27:02,907 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:430]
2025-06-04 11:27:02,907 INFO: 星期几: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:436]
2025-06-04 11:27:02,908 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:445]
2025-06-04 11:27:02,909 INFO: 周菜单 39 中找到 6 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:456]
2025-06-04 11:27:02,910 INFO: 分析食谱: 🏫 三鲜菠菜, recipe_id=397 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,911 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,920 INFO: 分析食谱: 🏫 西红柿炒蛋, recipe_id=401 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,922 INFO: 找到 2 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,925 INFO: 分析食谱: 🏫 米饭, recipe_id=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,926 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,928 INFO: 分析食谱: 🏫 花菜炒肉, recipe_id=412 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,930 INFO: 找到 3 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,935 INFO: 分析食谱: 🏫 芹菜炒肉, recipe_id=409 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,936 INFO: 找到 3 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,941 INFO: 分析食谱: 🏫 猪肉汤, recipe_id=411 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,942 INFO: 找到 5 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,967 INFO: 查询餐次: 晚餐 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:361]
2025-06-04 11:27:02,969 INFO: 查询结果: 找到 0 个菜单计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:370]
2025-06-04 11:27:02,971 INFO: 该区域所有菜单计划数量: 0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:374]
2025-06-04 11:27:02,971 INFO: 未找到日菜单，查询周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:430]
2025-06-04 11:27:02,972 INFO: 星期几: 3 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:436]
2025-06-04 11:27:02,973 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:445]
2025-06-04 11:27:02,975 INFO: 周菜单 39 中找到 5 个食谱 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:456]
2025-06-04 11:27:02,976 INFO: 分析食谱: 🏫 米饭, recipe_id=403 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,979 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:02,983 INFO: 分析食谱: 🏫 蒸蛋羹, recipe_id=405 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:02,985 INFO: 找到 5 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:03,153 INFO: 分析食谱: 🏫 炒大白菜, recipe_id=399 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:03,154 INFO: 找到 1 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:03,157 INFO: 分析食谱: 🏫 长豇豆烧茄子, recipe_id=380 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:03,159 INFO: 找到 7 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:27:03,170 INFO: 分析食谱: 🏫 酸辣土豆丝, recipe_id=381 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:464]
2025-06-04 11:27:03,171 INFO: 找到 6 个食材 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:471]
2025-06-04 11:30:47,510 INFO: 超级编辑器接收到的数据: [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:202]
2025-06-04 11:30:47,511 INFO:   area_id: 45 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:203]
2025-06-04 11:30:47,513 INFO:   warehouse_id: 7 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:204]
2025-06-04 11:30:47,514 INFO:   consumption_date: 2025-06-04 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:205]
2025-06-04 11:30:47,514 INFO:   meal_types: ['早餐', '午餐', '晚餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:206]
2025-06-04 11:30:47,514 INFO:   diners_count: 1000 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:207]
2025-06-04 11:30:47,515 INFO:   notes:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:208]
2025-06-04 11:30:47,517 INFO: 创建包含餐次 早餐+午餐+晚餐 的消耗计划 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:235]
2025-06-04 11:30:47,521 INFO: 未找到对应的菜单计划，将创建独立的消耗计划: area_id=45, date=2025-06-04, meal_types=['早餐', '午餐', '晚餐'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:252]
2025-06-04 11:30:47,525 INFO: 创建了消耗计划 ID: 36 (餐次: 早餐+午餐+晚餐) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\consumption_plan_super.py:277]
